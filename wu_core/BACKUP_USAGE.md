# Wu Core Backup 使用指南

## 概述

Wu Core 的備份功能已經過改進，可以在 Android 實體裝置上無需顯示權限對話框即可正常工作。

## 主要改進

### 1. 智能權限處理
- **自動檢測 Android 版本**：根據不同 Android 版本使用適當的權限策略
- **靜默權限請求**：在背景中請求權限，不打擾用戶
- **優雅降級**：即使權限被拒絕，仍可使用 app-specific 目錄

### 2. 安全的備份目錄
- **優先使用外部存儲**：`/Android/data/[package]/files/backups`
- **備用應用目錄**：如果外部存儲不可用
- **最終備用臨時目錄**：確保總是有可用的備份位置

### 3. 增強的錯誤處理
- **多層備用方案**：確保備份功能始終可用
- **詳細的錯誤信息**：便於調試和問題排查
- **自動恢復機制**：遇到問題時自動嘗試替代方案

## 使用方法

### 基本用法

```dart
import 'package:wu_core/wu_backup.dart';

// 創建備份實例
final backup = ZipBackup(backupPath, 'my_backup.zip');

// 執行備份
await backup.backup((tempDirPath) async {
  // 準備要備份的文件
  final file = File('$tempDirPath/data.json');
  await file.writeAsString(jsonEncode(myData));
  return ['data.json'];
});

// 執行還原（自動找到最新備份）
await backup.restore((restoredFilePath) async {
  if (restoredFilePath.endsWith('data.json')) {
    final content = await File(restoredFilePath).readAsString();
    myData = jsonDecode(content);
  }
});
```

### 權限處理

```dart
// 檢查並請求權限（靜默）
final hasPermission = await PermissionHelper.requestStoragePermissionSilently();

// 獲取安全的備份目錄
final safeDir = await PermissionHelper.getSafeBackupDirectory();

// 檢查目錄是否可寫
final isWritable = await PermissionHelper.isDirectoryWritable(myPath);
```

### 手動選擇備份文件

```dart
// 使用文件選擇器還原
await backup.restoreWithPicker((restoredFilePath) async {
  // 處理還原的文件
});
```

## Android 版本兼容性

### Android 13+ (API 33+)
- 使用新的媒體權限模型
- 主要依賴 app-specific 目錄
- 不需要 WRITE_EXTERNAL_STORAGE 權限

### Android 11-12 (API 30-32)
- 嘗試獲取 MANAGE_EXTERNAL_STORAGE 權限
- 備用使用基本存儲權限
- 支持 scoped storage

### Android 10 及以下 (API ≤29)
- 使用傳統的 WRITE_EXTERNAL_STORAGE 權限
- 完全向後兼容

## 測試

使用提供的測試應用程序：

```bash
cd wu_core/example
flutter run backup_example.dart
```

測試功能包括：
- 權限檢測
- 目錄可寫性測試
- 備份功能測試
- 還原功能測試

## 故障排除

### 常見問題

1. **備份失敗**
   - 檢查是否有足夠的存儲空間
   - 確認備份目錄路徑正確
   - 查看錯誤日誌

2. **權限被拒絕**
   - 應用會自動使用 app-specific 目錄
   - 不會影響備份功能的正常使用

3. **找不到備份文件**
   - 檢查備份是否成功完成
   - 確認備份目錄路徑
   - 使用手動選擇功能

### 調試信息

啟用調試模式查看詳細信息：

```dart
// 權限處理會輸出詳細的調試信息
// 包括 Android 版本、權限狀態等
```

## 最佳實踐

1. **總是使用安全目錄**：優先使用 `getSafeBackupDirectory()`
2. **處理異常**：備份/還原操作要包含適當的錯誤處理
3. **用戶反饋**：向用戶顯示操作進度和結果
4. **定期測試**：在不同 Android 版本上測試功能

## 注意事項

- 備份文件存儲在 app-specific 目錄中，卸載應用時會被刪除
- 如需永久保存，建議提供導出到用戶選擇位置的功能
- 大文件備份可能需要額外的進度指示器
