part of '../wu_backup.dart';

class PermissionHelper {
  /// 靜默請求存儲權限，不顯示對話框
  static Future<bool> requestStoragePermissionSilently() async {
    if (!Platform.isAndroid) {
      return true; // 非 Android 平台直接返回 true
    }

    try {
      final androidVersion = await _getAndroidApiLevel();
      print('Android API Level: $androidVersion');

      // Android 13 (API 33) 及以上版本使用新的權限模型
      if (androidVersion >= 33) {
        // Android 13+ 主要使用 app-specific 目錄，不需要特殊權限
        // 但如果需要訪問其他應用的媒體文件，需要新的權限
        final photos = await Permission.photos.status;
        final videos = await Permission.videos.status;

        if (photos.isDenied || videos.isDenied) {
          final results = await [
            Permission.photos,
            Permission.videos,
          ].request();

          // 即使被拒絕，我們仍然可以使用 app-specific 目錄
          print('Media permissions: photos=${results[Permission.photos]}, videos=${results[Permission.videos]}');
        }

        return true; // 總是返回 true，因為可以使用 app-specific 目錄
      }

      // Android 11-12 (API 30-32) 版本
      if (androidVersion >= 30) {
        // 檢查 MANAGE_EXTERNAL_STORAGE 權限
        final manageStorageStatus = await Permission.manageExternalStorage.status;

        if (manageStorageStatus.isDenied) {
          // 嘗試請求權限，但不強制要求
          await Permission.manageExternalStorage.request();
        }

        // 檢查基本存儲權限
        final storageStatus = await Permission.storage.status;
        if (storageStatus.isDenied) {
          await Permission.storage.request();
        }

        return true; // 即使權限被拒絕，也可以使用 app-specific 目錄
      }

      // Android 10 及以下版本 (API 29 及以下)
      final storageStatus = await Permission.storage.status;

      if (storageStatus.isDenied) {
        final result = await Permission.storage.request();
        print('Storage permission result: $result');

        // 即使被拒絕，也返回 true，因為可以使用 app-specific 目錄
        return true;
      }

      return storageStatus.isGranted;
    } catch (e) {
      print('權限請求失敗: $e');
      // 即使權限請求失敗，也返回 true，因為可以使用 app-specific 目錄
      return true;
    }
  }

  /// 獲取 Android API 級別
  static Future<int> _getAndroidApiLevel() async {
    if (!Platform.isAndroid) return 0;

    try {
      // 通過檢查權限可用性來推斷 Android 版本
      // Android 13+ 有 photos/videos 權限
      final hasPhotosPermission = await Permission.photos.status != PermissionStatus.permanentlyDenied;
      final hasManageStorage = await Permission.manageExternalStorage.status != PermissionStatus.permanentlyDenied;

      if (hasPhotosPermission) {
        return 33; // Android 13+
      } else if (hasManageStorage) {
        return 30; // Android 11+
      } else {
        return 29; // Android 10 或更早
      }
    } catch (e) {
      // 如果無法檢測，使用保守策略
      return 30; // 假設是 Android 11
    }
  }

  /// 獲取安全的備份目錄路徑（不需要權限）
  static Future<String> getSafeBackupDirectory() async {
    try {
      if (Platform.isAndroid) {
        // 優先使用 app-specific 外部存儲目錄，不需要權限
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null && await externalDir.exists()) {
          final backupDir = Directory('${externalDir.path}/backups');
          try {
            if (!await backupDir.exists()) {
              await backupDir.create(recursive: true);
            }
            // 測試是否可寫
            final testFile = File('${backupDir.path}/.test');
            await testFile.writeAsString('test');
            await testFile.delete();
            return backupDir.path;
          } catch (e) {
            // 如果外部存儲不可用，繼續嘗試其他選項
            print('外部存儲不可用: $e');
          }
        }
      }

      // 備用方案1：使用應用文檔目錄
      try {
        final appDocDir = await getApplicationDocumentsDirectory();
        final backupDir = Directory('${appDocDir.path}/backups');
        if (!await backupDir.exists()) {
          await backupDir.create(recursive: true);
        }
        // 測試是否可寫
        final testFile = File('${backupDir.path}/.test');
        await testFile.writeAsString('test');
        await testFile.delete();
        return backupDir.path;
      } catch (e) {
        print('應用文檔目錄不可用: $e');
      }

      // 最後備用方案：使用臨時目錄
      final tempDir = await getTemporaryDirectory();
      final backupDir = Directory('${tempDir.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }
      return backupDir.path;
    } catch (e) {
      print('獲取備份目錄失敗: $e');
      // 緊急備用方案：直接使用臨時目錄
      final tempDir = await getTemporaryDirectory();
      return tempDir.path;
    }
  }

  /// 獲取可用的備份文件列表（從安全目錄）
  static Future<List<String>> getAvailableBackupFiles(String backupDir) async {
    try {
      final dir = Directory(backupDir);
      if (!await dir.exists()) {
        return [];
      }

      final files = await dir.list().where((entity) => entity is File && entity.path.endsWith('.zip')).toList();

      return files.map((file) => file.path).toList();
    } catch (e) {
      print('獲取備份文件列表失敗: $e');
      return [];
    }
  }

  /// 檢查文件是否可讀
  static Future<bool> isFileReadable(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists() && await file.length() > 0;
    } catch (e) {
      return false;
    }
  }

  /// 檢查目錄是否可寫
  static Future<bool> isDirectoryWritable(String dirPath) async {
    try {
      // 檢查路徑是否為空或無效
      if (dirPath.isEmpty) return false;

      final dir = Directory(dirPath);

      // 嘗試創建目錄
      if (!await dir.exists()) {
        try {
          await dir.create(recursive: true);
        } catch (e) {
          // 如果無法創建目錄，返回 false
          return false;
        }
      }

      // 嘗試創建一個測試文件
      final testFile = File('$dirPath/.test_write_${DateTime.now().millisecondsSinceEpoch}');
      try {
        await testFile.writeAsString('test');
        await testFile.delete();
        return true;
      } catch (e) {
        // 如果無法寫入文件，嘗試清理並返回 false
        try {
          if (await testFile.exists()) {
            await testFile.delete();
          }
        } catch (_) {
          // 忽略清理錯誤
        }
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
