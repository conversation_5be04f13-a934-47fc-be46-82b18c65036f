part of '../wu_backup.dart';

class PermissionHelper {
  /// 靜默請求存儲權限，不顯示對話框
  static Future<bool> requestStoragePermissionSilently() async {
    if (!Platform.isAndroid) {
      return true; // 非 Android 平台直接返回 true
    }

    try {
      // Android 13 (API 33) 及以上版本使用新的權限模型
      if (await _isAndroid13OrAbove()) {
        // Android 13+ 不需要 WRITE_EXTERNAL_STORAGE 權限
        // 只需要 READ_MEDIA_* 權限，但我們使用 app-specific 目錄
        return true;
      }

      // Android 12 及以下版本
      final storageStatus = await Permission.storage.status;
      final manageStorageStatus = await Permission.manageExternalStorage.status;

      // 如果已經有權限，直接返回
      if (storageStatus.isGranted || manageStorageStatus.isGranted) {
        return true;
      }

      // 嘗試靜默請求權限
      final results = await [
        Permission.storage,
        Permission.manageExternalStorage,
      ].request();

      // 檢查是否至少有一個權限被授予
      return results[Permission.storage]?.isGranted == true ||
          results[Permission.manageExternalStorage]?.isGranted == true;
    } catch (e) {
      print('權限請求失敗: $e');
      return false;
    }
  }

  /// 檢查是否為 Android 13 或以上版本
  static Future<bool> _isAndroid13OrAbove() async {
    if (!Platform.isAndroid) return false;

    try {
      // 這裡可以通過檢查 API 級別來判斷
      // 簡化處理：假設新版本 Flutter 運行在較新的 Android 上
      return true; // 保守處理，假設是新版本
    } catch (e) {
      return false;
    }
  }

  /// 獲取安全的備份目錄路徑（不需要權限）
  static Future<String> getSafeBackupDirectory() async {
    try {
      if (Platform.isAndroid) {
        // 使用 app-specific 外部存儲目錄，不需要權限
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          final backupDir = Directory('${externalDir.path}/backups');
          if (!await backupDir.exists()) {
            await backupDir.create(recursive: true);
          }
          return backupDir.path;
        }
      }

      // 備用方案：使用應用文檔目錄
      final appDocDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${appDocDir.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }
      return backupDir.path;
    } catch (e) {
      print('獲取備份目錄失敗: $e');
      // 最後備用方案：使用臨時目錄
      final tempDir = await getTemporaryDirectory();
      return tempDir.path;
    }
  }

  /// 獲取可用的備份文件列表（從安全目錄）
  static Future<List<String>> getAvailableBackupFiles(String backupDir) async {
    try {
      final dir = Directory(backupDir);
      if (!await dir.exists()) {
        return [];
      }

      final files = await dir.list().where((entity) => entity is File && entity.path.endsWith('.zip')).toList();

      return files.map((file) => file.path).toList();
    } catch (e) {
      print('獲取備份文件列表失敗: $e');
      return [];
    }
  }

  /// 檢查文件是否可讀
  static Future<bool> isFileReadable(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists() && await file.length() > 0;
    } catch (e) {
      return false;
    }
  }

  /// 檢查目錄是否可寫
  static Future<bool> isDirectoryWritable(String dirPath) async {
    try {
      final dir = Directory(dirPath);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      // 嘗試創建一個測試文件
      final testFile = File('$dirPath/.test_write');
      await testFile.writeAsString('test');
      await testFile.delete();
      return true;
    } catch (e) {
      return false;
    }
  }
}
