import 'package:flutter/material.dart';
import 'package:wu_core/wu_backup.dart';
import 'dart:io';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Backup Test',
      home: const BackupTestPage(),
    );
  }
}

class BackupTestPage extends StatefulWidget {
  const BackupTestPage({super.key});

  @override
  State<BackupTestPage> createState() => _BackupTestPageState();
}

class _BackupTestPageState extends State<BackupTestPage> {
  String _status = '準備測試...';
  String _backupPath = '';

  @override
  void initState() {
    super.initState();
    _initializeBackupPath();
  }

  Future<void> _initializeBackupPath() async {
    try {
      final safeDir = await PermissionHelper.getSafeBackupDirectory();
      setState(() {
        _backupPath = safeDir;
        _status = '備份目錄已設定: $safeDir';
      });
    } catch (e) {
      setState(() {
        _status = '初始化失敗: $e';
      });
    }
  }

  Future<void> _testPermissions() async {
    setState(() {
      _status = '測試權限中...';
    });

    try {
      final hasPermission = await PermissionHelper.requestStoragePermissionSilently();
      final isWritable = await PermissionHelper.isDirectoryWritable(_backupPath);

      setState(() {
        _status = '權限測試結果:\n'
            '- 存儲權限: ${hasPermission ? "已獲得" : "未獲得"}\n'
            '- 目錄可寫: ${isWritable ? "是" : "否"}\n'
            '- 備份目錄: $_backupPath';
      });
    } catch (e) {
      setState(() {
        _status = '權限測試失敗: $e';
      });
    }
  }

  Future<void> _testBackup() async {
    setState(() {
      _status = '執行備份測試中...';
    });

    try {
      final zipBackup = ZipBackup(_backupPath, 'test_backup.zip');

      await zipBackup.backup((tempDirPath) async {
        // 創建一個測試文件
        final testFile = File('$tempDirPath/test_data.txt');
        await testFile.writeAsString('這是測試備份數據\n時間: ${DateTime.now()}');
        return ['test_data.txt'];
      });

      setState(() {
        _status = '備份測試成功完成！\n備份文件位置: $_backupPath/test_backup.zip';
      });
    } catch (e) {
      setState(() {
        _status = '備份測試失敗: $e';
      });
    }
  }

  Future<void> _testRestore() async {
    setState(() {
      _status = '執行還原測試中...';
    });

    try {
      final zipBackup = ZipBackup(_backupPath, 'test_backup.zip');

      String restoredContent = '';
      await zipBackup.restore((restoredFilePath) async {
        if (restoredFilePath.endsWith('test_data.txt')) {
          final file = File(restoredFilePath);
          restoredContent = await file.readAsString();
        }
      });

      setState(() {
        _status = '還原測試成功完成！\n還原內容: $restoredContent';
      });
    } catch (e) {
      setState(() {
        _status = '還原測試失敗: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('備份權限測試'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  _status,
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _testPermissions,
              child: Text('測試權限'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testBackup,
              child: Text('測試備份'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testRestore,
              child: Text('測試還原'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _initializeBackupPath,
              child: Text('重新初始化'),
            ),
          ],
        ),
      ),
    );
  }
}
