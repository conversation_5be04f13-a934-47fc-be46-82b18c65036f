import 'package:test/test.dart';
import 'package:dio/dio.dart';
import '../lib/core/llm_gemini.dart';

void main() {
  group('Gemini Error Handling Tests', () {
    late LlmGemini gemini;

    setUp(() {
      gemini = LlmGemini();
    });

    test('should handle 503 model overloaded error', () {
      // 模擬 503 錯誤響應
      final mockResponse = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 503,
        data: {
          "error": {
            "code": 503,
            "message": "The model is overloaded. Please try again later.",
            "status": "UNAVAILABLE"
          }
        },
      );

      final dioException = DioException(
        requestOptions: RequestOptions(path: '/test'),
        response: mockResponse,
        type: DioExceptionType.badResponse,
      );

      final errorMessage = gemini.handleError(dioException);
      
      expect(errorMessage, contains('模型目前過載'));
      expect(errorMessage, contains('請稍後再試'));
      expect(errorMessage, contains('建議等待 1-2 分鐘'));
    });

    test('should handle 429 rate limit error', () {
      final mockResponse = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 429,
        data: {
          "error": {
            "code": 429,
            "message": "Too many requests",
            "status": "RESOURCE_EXHAUSTED"
          }
        },
      );

      final dioException = DioException(
        requestOptions: RequestOptions(path: '/test'),
        response: mockResponse,
        type: DioExceptionType.badResponse,
      );

      final errorMessage = gemini.handleError(dioException);
      
      expect(errorMessage, contains('API 請求頻率過高'));
    });

    test('should handle 401 authentication error', () {
      final mockResponse = Response(
        requestOptions: RequestOptions(path: '/test'),
        statusCode: 401,
        data: {
          "error": {
            "code": 401,
            "message": "Invalid API key",
            "status": "UNAUTHENTICATED"
          }
        },
      );

      final dioException = DioException(
        requestOptions: RequestOptions(path: '/test'),
        response: mockResponse,
        type: DioExceptionType.badResponse,
      );

      final errorMessage = gemini.handleError(dioException);
      
      expect(errorMessage, contains('API 金鑰無效'));
    });

    test('should handle connection timeout', () {
      final dioException = DioException(
        requestOptions: RequestOptions(path: '/test'),
        type: DioExceptionType.connectionTimeout,
      );

      final errorMessage = gemini.handleError(dioException);
      
      expect(errorMessage, contains('連接 Gemini 服務超時'));
    });

    test('should handle unknown errors', () {
      final errorMessage = gemini.handleError('Some unknown error');
      
      expect(errorMessage, contains('未知錯誤'));
    });
  });
}
