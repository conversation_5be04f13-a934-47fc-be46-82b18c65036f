import 'dart:convert';
import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_base.dart';

/// Ollama 服務實現
class LlmOllama extends LlmBase {
  static const String _baseUrl = 'http://localhost:11434/api';

  LlmOllama()
      : super(
          serviceName: 'Ollama',
          recommandModels: [],
          apiKeyUrl: 'https://ollama.com/download',
        );

  @override
  Future<List<String>> getModels(String apiKey) async {
    final dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json'},
      ),
    );

    final response = await dio.get('/tags');
    final data = response.data as Map<String, dynamic>;
    final models = (data['models'] as List).map((model) => model['name'] as String).toList();
    return models;
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
        LlmRole.system => 'system',
        LlmRole.user => 'user',
        LlmRole.assistant => 'assistant',
      };

  @override
  Future<String?> futureText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      final response = await dio.post(
        '/chat',
        data: {'model': modelName, 'messages': formatMessages(messages), 'stream': false},
      );

      final result = response.data['message']['content'];
      return result ?? '';
    } catch (e) {
      return handleError(e);
    }
  }

  @override
  Stream<String?> streamText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async* {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      final response = await dio.post(
        '/chat',
        data: {
          'model': modelName,
          'messages': formatMessages(messages),
          'stream': true,
        },
        options: Options(responseType: ResponseType.stream),
      );

      final stream = response.data.stream as Stream<List<int>>;
      String buffer = '';

      await for (final chunk in stream) {
        buffer += utf8.decode(chunk);
        final lines = buffer.split('\n');
        buffer = lines.last;

        for (final line in lines) {
          if (line.trim().isEmpty) continue;

          try {
            final jsonResponse = jsonDecode(line);
            if (jsonResponse is Map<String, dynamic>) {
              // Ollama streaming response format: {"message":{"role":"assistant","content":"text"},"done":false}
              final message = jsonResponse['message'];
              if (message != null && message is Map<String, dynamic>) {
                final content = message['content'] as String?;
                if (content != null && content.isNotEmpty) {
                  yield content;
                }
              }

              // Check if streaming is done
              final done = jsonResponse['done'] as bool?;
              if (done == true) {
                break;
              }
            }
          } catch (e) {
            // Ignore JSON parsing errors for incomplete chunks
            continue;
          }
        }
      }
    } catch (e) {
      yield handleError(e);
    }
  }
}
