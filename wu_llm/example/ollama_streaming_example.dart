import 'dart:io';
import 'package:wu_llm/wu_llm.dart';

/// Example demonstrating how to use Ollama streaming text generation
///
/// Prerequisites:
/// 1. Install Ollama: https://ollama.com/download
/// 2. Pull a model: `ollama pull llama2` or `ollama pull mistral`
/// 3. Start Ollama service (usually runs automatically)
///
/// Usage:
/// ```bash
/// dart run example/ollama_streaming_example.dart
/// ```
void main() async {
  // Create Ollama service instance
  final ollama = LlmOllama();

  print('=== Ollama Streaming Example ===');
  print('Service: ${ollama.serviceName}');
  print('API Key URL: ${ollama.apiKeyUrl}');
  print('');

  // Prepare messages for conversation
  final messages = [
    LlmMessage(role: LlmRole.system, content: 'You are a helpful assistant. Please respond concisely.'),
    LlmMessage(role: LlmRole.user, content: 'Tell me a short story about a robot learning to paint.'),
  ];

  try {
    print('🤖 Starting streaming conversation...');
    print('📝 User: ${messages.last.content}');
    stdout.write('🤖 Assistant: ');

    // Stream the response
    final stream = ollama.streamText(
      apiKey: 'not-required-for-ollama', // Ollama doesn't require API key
      modelName: 'llama2', // Change to your available model
      messages: messages,
    );

    // Process each chunk as it arrives
    await for (final chunk in stream) {
      if (chunk != null) {
        // Write each chunk without newline to show streaming effect
        stdout.write(chunk);
      }
    }

    print('\n\n✅ Streaming completed successfully!');
  } catch (e) {
    print('\n❌ Error occurred: $e');
    print('\n💡 Make sure:');
    print('   1. Ollama is installed and running');
    print('   2. The model "llama2" is available (run: ollama pull llama2)');
    print('   3. Ollama service is accessible at http://localhost:11434');
  }
}
