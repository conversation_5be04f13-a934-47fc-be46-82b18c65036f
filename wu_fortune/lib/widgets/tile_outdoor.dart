import 'package:flutter/material.dart';
import 'package:wu_core/wu_extensions.dart';

import '../wu_qimen.dart';
import '../wu_widgets.dart';

class TileOutdoor extends StatelessWidget {
  final DateTime solar;
  final int rowCount;
  final Color colorLucky;
  final Color colorMoney;
  final Color colorBad1;
  final Color colorBad2;
  final Color colorKong;
  final double textScale;
  const TileOutdoor({
    super.key,
    required this.solar,
    required this.rowCount,
    this.colorLucky = const Color.fromARGB(255, 106, 37, 37),
    this.colorMoney = const Color.fromARGB(255, 152, 116, 0),
    this.colorBad1 = const Color.fromARGB(255, 53, 141, 56),
    this.colorBad2 = const Color.fromARGB(255, 21, 101, 166),
    this.colorKong = const Color.fromARGB(255, 158, 158, 158),
    this.textScale = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final nowGzDate = GzDate.bySolar(now);
    final wdate = DateTime(solar.year, solar.month, solar.day, 8);
    // final gzdate = GzDate.bySolar(wdate);
    // final ggz = Guiguzi.infos[gzdate.d.name];
    final colkeys = ['時', '休', '生', '開', '景'];
    final emojis = {
      "正北": "⬆️", "正南": "⬇️", "正東": "➡️", "正西": "⬅️", //
      "東北": "↗️", "西南": "↙️", "西北": "↖️", "東南": "️️️️↘️", //
    };
    // 考慮加入奇門四害顯示、五不遇時
    return DefaultTextStyle(
      style: TextStyle(color: Colors.black),
      child: Container(
        color: Colors.white,
        constraints: BoxConstraints(minWidth: 200),
        child: Column(
          children: [
            Text('吉方出行表'),
            StreamBuilder(
              stream: getOutdoor(wdate, 6),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) return CircularProgressIndicator();
                if (snapshot.hasData == false) return Text('No data');
                final data = snapshot.data!;
                return ConstrainedBox(
                  constraints: BoxConstraints(minWidth: 200),
                  child: Table(
                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                    columnWidths: {0: FixedColumnWidth(46 * textScale)},
                    border: TableBorder.all(color: Colors.grey),
                    children: [
                          TableRow(
                              children: colkeys
                                  .map((e) => Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          e,
                                          textAlign: TextAlign.center,
                                          textScaler: TextScaler.linear(textScale),
                                          style: TextStyle(height: 1.0, fontSize: 12),
                                        ),
                                      ))
                                  .toList())
                        ] +
                        data.map((e) {
                          Color rcolor = Colors.transparent;
                          // 反吟伏吟
                          if (e['吟']?.isNotEmpty ?? false) rcolor = colorBad2.withAlpha(60);
                          // 五不遇時
                          if (e['特']?.isNotEmpty ?? false) rcolor = colorBad1.withAlpha(60);
                          // 當前時間
                          final zhiHour = Zhi.byName(e['時']);
                          if ((now.ymd() == wdate.ymd()) && (nowGzDate.h.zhi.index == zhiHour.index)) {
                            rcolor = Colors.red.withAlpha(60);
                          }
                          return TableRow(
                            decoration: BoxDecoration(color: rcolor),
                            children: colkeys.map((key) {
                              final value = e[key]!;
                              if (key == '時') {
                                return Center(
                                  child: ZhiRender(zhiName: value, textScale: textScale),
                                );
                              }
                              var textColor = Colors.black;
                              // if (value == ggz?['喜神']) textColor = colorLucky;
                              // if (value == ggz?['財神']) textColor = colorMoney;
                              final text = Gua8.byName(value.p['後天']).direction;
                              var cellColor = Colors.transparent;
                              if (value.p["空"] ?? false) cellColor = colorKong;
                              return Container(
                                height: 28 * textScale,
                                color: cellColor,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      text,
                                      textScaler: TextScaler.linear(textScale),
                                      style: TextStyle(fontSize: 16, height: 1.0, color: textColor),
                                      textAlign: TextAlign.center,
                                    ),
                                    Text(
                                      emojis[text] ?? '',
                                      textScaler: TextScaler.linear(textScale),
                                      style: TextStyle(fontSize: 16, height: 1.0, color: textColor),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          );
                        }).toList(),
                  ),
                );
              },
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              spacing: 8,
              children: [
                Container(
                  color: colorKong,
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Text('空', style: TextStyle(height: 1.0)),
                ),
                Container(
                  color: colorBad1.withAlpha(60),
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Text('五不遇時', style: TextStyle(height: 1.0)),
                ),
                Container(
                  color: colorBad2.withAlpha(60),
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Text('反吟伏吟', style: TextStyle(height: 1.0)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Stream<List<Map<String, dynamic>>> getOutdoor(DateTime solar, int rowCount) async* {
    List<Map<String, dynamic>> outdoorList = [];
    for (int i = 0; i < rowCount; i++) {
      final workSolar = solar.add(Duration(hours: 2 * i));
      final desk = QimenBuilder.build(deskMode: DeskMode.yangHour, solar: workSolar);
      final gzdate = desk.gzdate;
      final rec = <String, dynamic>{};
      rec['時'] = gzdate.h.zhi.name;
      for (final door in ['休', '生', '開', '景']) {
        final cell = desk.cellFirst('門', door)!;
        rec[door] = cell;
      }
      // 考慮加入奇門四害顯示、五不遇時
      final (name, _) = specialTime(gzdate);
      if (name == "五不遇時") rec["特"] = "五不遇時";
      final ffyin = [desk.fuyin(), desk.fanyin()].join(" ").trim();
      if (ffyin.isNotEmpty) rec["吟"] = ffyin;
      outdoorList.add(rec);
      yield outdoorList;
    }
  }
}
