import 'package:flutter/material.dart';

import '../wu_calendar.dart';

class TileJieqi extends StatelessWidget {
  final int year;
  final double textScale;
  const T<PERSON><PERSON><PERSON><PERSON>({super.key, required this.year, this.textScale = 1.0});

  @override
  Widget build(BuildContext context) {
    final current = Jieqi.bySolar(DateTime.now());
    final currColor = Colors.red[200]!;
    return Table(
      border: TableBorder.all(color: Colors.grey),
      children: List.generate(12, (month) {
        final monthStr = zhMonthFull[month];
        final jq1 = Jieqi.byYearIndex(year, month * 2);
        final jq1Color = jq1.index == current.index ? currColor : Colors.transparent;
        final jq2 = Jieqi.byYearIndex(year, month * 2 + 1);
        final jq2Color = jq2.index == current.index ? currColor : Colors.transparent;
        return TableRow(children: [
          Padding(
            padding: const EdgeInsets.all(2.0),
            child: Text(
              monthStr,
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            color: jq1Color,
            padding: const EdgeInsets.all(2.0),
            child: Text(
              jq1.name,
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            color: jq1Color,
            padding: const EdgeInsets.all(2.0),
            child: Text(
              jq1.dateTime.day.toString(),
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            color: jq2Color,
            padding: const EdgeInsets.all(2.0),
            child: Text(
              jq2.name,
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            color: jq2Color,
            padding: const EdgeInsets.all(2.0),
            child: Text(
              jq2.dateTime.day.toString(),
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
        ]);
      }),
    );
  }
}
