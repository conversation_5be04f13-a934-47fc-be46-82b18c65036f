part of 'reader_base.dart';

class <PERSON><PERSON><PERSON><PERSON><PERSON> extends ReaderBase {
  ReaderLiuyao();

  @override
  String get feature => '''
## 六爻卜卦解讀
### 內容：
透過投擲銅錢或蓍草起卦，產生本卦與變卦，並結合六爻、六親、世應、動爻、納甲五行來進行詳盡分析。

### 術語：
- 本卦／變卦：呈現事情的本質與變化。
- 世爻／應爻：代表主體與對象。
- 六親：如兄弟、父母、子孫等象徵。

### 目的：
預測吉凶、判斷具體事件（如求職、婚姻、訴訟）。

### 特色：
- 精密、嚴謹、操作複雜。
- 目前最廣泛應用的預測系統。
- 可精準至時間點或應驗層次。''';

  @override
  (String, String) getPrompt(YijingModel model) {
    String sysPrompt = """
你是六爻專家，用簡單的話解讀卦象。直接說結果，不要分析過程。

重點：
- 用神是關鍵，看它旺不旺
- 世爻是你，應爻是對方或事情
- 直接說吉凶，不要解釋原理

格式：
```markdown
## 摘要
用30字以內總結卦象的狀況

## 用神狀況
直接說用神旺不旺，對你有利還是不利

## 世應關係
直接說你和對方/事情的關係如何

## 重要變化
直接說哪些爻在動，會帶來什麼影響

## 吉凶判斷
直接說結果是好是壞

## 時間預測
如果能判斷，直接說大概什麼時候

## 實用建議
簡單說該怎麼做
```
""";

    final setting = Get.find<SettingController>();
    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    final gzDict = GzDate.toDict(model.divineAtGzDate ?? '');
    final layout = yijing.getLiuyaoLayout(GanZhi.byName(gzDict["月"]), GanZhi.byName(gzDict["日"]));
    String userPrompt = """
${setting.prefixPrompt}
---
* 占卦時間： ${model.divineAtGzDate}
* 問題： ${model.question}
* 本卦： ${layout["本卦"].fullname}
* 變卦： ${layout["變卦"].fullname}
* 旬空： ${layout["空亡"]}
* 月破： ${layout["月破"]}
* 暗動： ${layout["暗動"]}
* 世爻： ${layout["世爻"]}
* 應爻： ${layout["應爻"]}
## 爻位從上至下
""";
    final yaoList = layout["爻"] as List<Map<String, dynamic>>;
    for (var yaoIndex = 0; yaoIndex < yaoList.length; yaoIndex++) {
      final yao = yaoList[yaoIndex];
      userPrompt += "\n### ${yao["爻名"]}爻\n";
      userPrompt += "- 六獸：${yao["六獸"]}\n";
      userPrompt += "- 符號：${yao["符號"]}\n";
      userPrompt += "- 本爻：${yao["本爻支"]}${yao["本爻親"]}\n";
      userPrompt += "- 變爻：${yao["變爻支"]}${yao["變爻親"]}\n";
      userPrompt += "- 伏爻：${yao["伏爻支"]}${yao["伏爻親"]}\n";
    }

    final godList = layout["神煞"] as Map<String, String>;
    userPrompt += "\n## 神煞\n";
    for (var god in godList.entries) {
      userPrompt += "- ${god.key}：${god.value}\n";
    }
    return (sysPrompt, userPrompt);
  }
}
