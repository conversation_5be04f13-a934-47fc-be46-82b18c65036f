import 'dart:io';

import 'package:archive/archive.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';

typedef PrepareBackupFiles = Future<List<String>> Function(String tempDirPath);
typedef OnFileRestored = void Function(String restoredFilePath);

class ZipBackup {
  final String backupPath;
  final String zipFileName;

  ZipBackup(this.backupPath, this.zipFileName);

  /// 將所要備份的資料保存至暫存資料夾，然後壓縮成 zip 檔案，然後再將 zip 檔案複製到指定的備份資料夾中
  Future<void> backup(PrepareBackupFiles prepareBackupFiles) async {
    final tempDir = await getTemporaryDirectory();
    final zipFilePath = '${tempDir.path}/$zipFileName';

    // 將所有要備份的檔案保存至暫存資料夾
    final backupFiles = await prepareBackupFiles(tempDir.path);

    // 將所有檔案打包成 zip 檔案，並將 zip 檔案寫入暫存資料夾
    final archive = Archive();
    for (final filename in backupFiles) {
      final file = File('${tempDir.path}/$filename');
      if (!file.existsSync()) {
        throw Exception('備份檔案不存在: $filename');
      }
      final bytes = file.readAsBytesSync();
      final name = file.path.split('/').last;
      print('add file: $name');
      archive.addFile(ArchiveFile(name, bytes.length, bytes));
    }
    final encoder = ZipEncoder();
    final zipBytes = encoder.encode(archive);
    await File(zipFilePath).writeAsBytes(zipBytes);
    print('zip file created: $zipFilePath');

    // 確保備份資料夾存在
    final backupDir = Directory(backupPath);
    if (!backupDir.existsSync()) {
      await backupDir.create(recursive: true);
    }

    // 將 zip 檔案複製到指定的備份資料夾中
    final finalBackupPath = '$backupPath/$zipFileName';
    await File(zipFilePath).copy(finalBackupPath);
    print('backup completed: $finalBackupPath');
  }

  Future<void> restore(OnFileRestored onFileRestored) async {
    // 讓使用者選擇要還原的備份檔案
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['zip'],
      initialDirectory: backupPath,
      dialogTitle: '選擇備份檔案',
    );

    if (result == null || result.files.isEmpty) {
      throw Exception('未選擇備份檔案');
    }

    final selectedFilePath = result.files.first.path;
    if (selectedFilePath == null) {
      throw Exception('無法取得檔案路徑');
    }

    final zipFile = File(selectedFilePath);
    if (!zipFile.existsSync()) {
      throw Exception('備份檔案不存在: $selectedFilePath');
    }

    // 將 zip 檔案解壓縮到暫存資料夾
    final tempDir = await getTemporaryDirectory();
    final bytes = zipFile.readAsBytesSync();
    final archive = ZipDecoder().decodeBytes(bytes);

    for (final file in archive) {
      final filename = file.name;
      final data = file.content as List<int>;

      // 將檔案解壓縮到暫存資料夾
      final tempFilePath = '${tempDir.path}/$filename';
      await File(tempFilePath).writeAsBytes(data);

      // 呼叫回調函式，傳入暫存資料夾中的檔案路徑
      onFileRestored(tempFilePath);
    }
  }
}
