import 'package:wu_fortune/wu_calendar.dart';

// import '../controllers/setting_controller.dart';
import '../controllers/setting_controller.dart';
import '../models/yijing_model.dart';

part 'reader_gaodao.dart';
part 'reader_liuyao.dart';
part 'reader_tiyong.dart';
part 'reader_yijing.dart';

abstract class ReaderBase {
  ReaderBase();

  /// 解讀方式的特點
  String get feature;

  /// 取得提示詞
  (String systemPrompt, String userPrompt) getPrompt(YijingModel model);

  /// 取得解讀方式
  static ReaderBase getReader(ReadingMode mode) {
    return switch (mode) {
      ReadingMode.yijing => ReaderYijing(),
      ReadingMode.gaodao => ReaderGaodao(),
      ReadingMode.tiyong => ReaderTiyong(),
      ReadingMode.liuyao => ReaderLiuyao(),
    };
  }

  /// 取出 摘要 內容
  String getSummary(String response) {
    final pattern = RegExp(r'## 摘要\n([\s\S]*?)(?=\n##|\Z)'); // 捕獲摘要內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 取出 markdown 內容
  String getMarkdownInner(String response) {
    final pattern = RegExp(r'```markdown\n([\s\S]*?)```'); // 捕獲 markdown 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 取出 JSON 內容
  String getJsonInner(String response) {
    final pattern = RegExp(r'```json\n([\s\S]*?)```'); // 捕獲 JSON 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 移除<think></think>之間的內容
  String removeThink(String response) {
    return response.replaceAll(RegExp(r'<think>[\s\S]*?</think>'), '');
  }
}
