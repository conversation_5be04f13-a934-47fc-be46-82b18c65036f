part of 'reader_base.dart';

class <PERSON>Gaoda<PERSON> extends ReaderBase {
  ReaderGaodao();

  @override
  String get feature => '''
## 高島吞象解讀
### 內容：
日本明治時代的易學家高島吞象，將卦象與歷史人物、事件對應，建立通俗而具體的象徵解釋。

### 目的：
提供直觀的卦意解釋，可作為占卜輔助。

### 特色：
- 每卦附有故事、名人事例。
- 有明顯的吉凶判斷與建議。
- 適合初學者快速理解卦意。
''';

  @override
  (String, String) getPrompt(YijingModel model) {
    String sysPrompt = """
你是一位易經老師，用簡單易懂的方式解讀卦象。請直接告訴我結果，不要分析過程。

請用這樣的方式回答：
1. **現在的狀況**：本卦告訴我什麼
2. **需要注意的變化**：變爻提醒我什麼
3. **最終結果**：變卦顯示會怎樣
4. **實際建議**：我該怎麼做

重點：
- 回答要口語化，像朋友聊天一樣，直接說重點就好。
- 不要有前綴的描述或提示詞。

格式：
```markdown
## 摘要
用30字以內總結卦象的狀況

## 現在的狀況
## 需要注意的變化
## 最終結果
## 實際建議
```
    """;

    final setting = Get.find<SettingController>();
    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    String userPrompt = """
${setting.prefixPrompt}
---
請為下列內容解讀卦象：
**問卦性別：** ${model.gender}
**占卦時間：** ${model.divineAtGzDate}
**問題：** ${model.question}
**本卦：** ${Gua64.bySign6(yijing.orginal).fullname}
**變卦：** ${Gua64.bySign6(yijing.changed).fullname}
**變爻：** ${yijing.dongYao.last}
""";
    return (sysPrompt, userPrompt);
  }
}
