// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_adapters.dart';

// **************************************************************************
// AdaptersGenerator
// **************************************************************************

class YijingModelAdapter extends TypeAdapter<YijingModel> {
  @override
  final typeId = 0;

  @override
  YijingModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return YijingModel(
      createAt: fields[0] as DateTime?,
      updateAt: fields[1] as DateTime?,
      gender: fields[2] == null ? "男" : fields[2] as String,
      question: fields[3] == null ? "" : fields[3] as String,
      additional: fields[4] as String?,
      divineAtSolar: fields[5] as String?,
      divineAtGzDate: fields[6] as String?,
      pickerData: fields[7] as PickerData?,
      readingMode:
          fields[8] == null ? ReadingMode.yijing : fields[8] as ReadingMode,
      readingYijing: fields[9] as String?,
      readingGaodao: fields[10] as String?,
      readingTiyong: fields[11] as String?,
      readingLiuyao: fields[12] as String?,
      summary: fields[13] as String?,
      comment: fields[14] as String?,
      tags: fields[15] == null ? const [] : (fields[15] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, YijingModel obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.createAt)
      ..writeByte(1)
      ..write(obj.updateAt)
      ..writeByte(2)
      ..write(obj.gender)
      ..writeByte(3)
      ..write(obj.question)
      ..writeByte(4)
      ..write(obj.additional)
      ..writeByte(5)
      ..write(obj.divineAtSolar)
      ..writeByte(6)
      ..write(obj.divineAtGzDate)
      ..writeByte(7)
      ..write(obj.pickerData)
      ..writeByte(8)
      ..write(obj.readingMode)
      ..writeByte(9)
      ..write(obj.readingYijing)
      ..writeByte(10)
      ..write(obj.readingGaodao)
      ..writeByte(11)
      ..write(obj.readingTiyong)
      ..writeByte(12)
      ..write(obj.readingLiuyao)
      ..writeByte(13)
      ..write(obj.summary)
      ..writeByte(14)
      ..write(obj.comment)
      ..writeByte(15)
      ..write(obj.tags);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is YijingModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PickerDataAdapter extends TypeAdapter<PickerData> {
  @override
  final typeId = 1;

  @override
  PickerData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PickerData(
      name: fields[0] == null ? '' : fields[0] as String,
      note: fields[1] == null ? '' : fields[1] as String,
      sign6: fields[2] == null ? '' : fields[2] as String,
    );
  }

  @override
  void write(BinaryWriter writer, PickerData obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.note)
      ..writeByte(2)
      ..write(obj.sign6);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PickerDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ReadingModeAdapter extends TypeAdapter<ReadingMode> {
  @override
  final typeId = 3;

  @override
  ReadingMode read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ReadingMode.yijing;
      case 1:
        return ReadingMode.gaodao;
      case 2:
        return ReadingMode.tiyong;
      case 3:
        return ReadingMode.liuyao;
      default:
        return ReadingMode.yijing;
    }
  }

  @override
  void write(BinaryWriter writer, ReadingMode obj) {
    switch (obj) {
      case ReadingMode.yijing:
        writer.writeByte(0);
      case ReadingMode.gaodao:
        writer.writeByte(1);
      case ReadingMode.tiyong:
        writer.writeByte(2);
      case ReadingMode.liuyao:
        writer.writeByte(3);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReadingModeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
