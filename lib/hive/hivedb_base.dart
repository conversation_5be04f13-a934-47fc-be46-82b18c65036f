import 'dart:io';

import 'package:adv_yijing/hive/hive_registrar.g.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

abstract class HiveDBBase<T> {
  final String boxName;
  HiveDBBase(this.boxName);

  Future<Box<T>> getBox() async {
    return await Hive.openBox<T>(boxName);
  }

  Future<void> clear() async {
    final box = await Hive.openBox<T>(boxName);
    final boxPath = box.path; // 获取 Box 文件的路径
    await box.close();

    try {
      await Hive.deleteBoxFromDisk(boxName, path: boxPath);
    } catch (e) {
      rethrow;
      // print(e);
    } finally {
      await box.close();
    }
  }

  Future<String> backupHiveBox(String backupDirectoryPath) async {
    final box = await Hive.openBox<T>(boxName);
    final boxPath = box.path; // 获取 Box 文件的路径
    await box.close(); // 备份前关闭 Box 以确保文件完整性
    final hivepath = "${boxName}.hive";
    final backupFilePath = '$backupDirectoryPath/$hivepath';

    try {
      await File(boxPath!).copy(backupFilePath); // 复制文件
      // print('Box "$boxName" backed up to $backupFilePath');
    } catch (e) {
      rethrow;
      // print('Error backing up box "$boxName": $e');
    } finally {
      await Hive.openBox<T>(boxName); // 重新打开 Box
    }
    return hivepath;
  }

  Future<void> restoreHiveBox(String backupDirectoryPath) async {
    final box = await Hive.openBox<T>(boxName);
    final boxPath = box.path;
    await box.close(); // 恢复前关闭 Box

    try {
      final backupFilePath = '$backupDirectoryPath/$boxName.hive';
      await File(backupFilePath).copy(boxPath!); // 将备份文件复制回原路径
      // print('Box "$boxName" restored from $backupFilePath');
    } catch (e) {
      // print('Error restoring box "$boxName": $e');
      rethrow;
    } finally {
      await Hive.openBox<T>(boxName); // 重新打开 Box
    }
  }

  static Future<void> init() async {
    final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
    Hive
      ..init(appDocumentsDir.path)
      ..registerAdapters();
  }
}
