import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import '../../core/markdown_style.dart';

class DocViewPage extends StatelessWidget {
  final String title;
  final String docPath;
  const DocViewPage({super.key, required this.docPath, required this.title});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text(title),
        ),
        body: FutureBuilder(
            future: rootBundle.loadString(docPath, cache: true),
            builder: (context, asyncSnapshot) {
              if (asyncSnapshot.hasData == false) return const CircularProgressIndicator();
              final doc = asyncSnapshot.data!;
              return Markdown(data: doc, styleSheet: markdownStyle());
            }),
      ),
    );
  }
}
