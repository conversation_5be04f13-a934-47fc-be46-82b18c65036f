import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'doc_view_page.dart';

final _docList = {
  "如何問卦": "assets/docs/如何問卦.md",
  "梅花易數體用法": "assets/docs/梅花易數.md",
  "高島柳解卦法": "assets/docs/高島吞象.md",
  "周易解卦法": "assets/docs/周易解卦.md",
  "奇門遁甲納氣法": "assets/docs/奇門納氣.md",
  "奇門三盤日常應用完整指南": "assets/docs/奇門三盤.md",
};

class DocsPage extends StatelessWidget {
  const DocsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Get.theme.colorScheme;
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('文獻'),
        ),
        body: ListView.builder(
          padding: const EdgeInsets.all(8.0),
          itemCount: _docList.length,
          itemBuilder: (context, index) {
            final e = _docList.entries.elementAt(index);
            return Card(
              color: colorScheme.secondaryContainer,
              child: ListTile(
                title: Text(e.key),
                onTap: () => Get.to(() => DocViewPage(title: e.key, docPath: e.value)),
              ),
            );
          },
        ),
      ),
    );
  }
}
