import 'package:adv_yijing/core/reader_base.dart';
import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:wu_fortune/wu_fortune.dart';
import 'package:wu_llm/wu_llm.dart';

import '../../core/markdown_style.dart';
import '../../models/yijing_model.dart';
import '../../controllers/setting_controller.dart';

class ReadingPage extends StatefulWidget {
  final ReadingMode mode;
  const ReadingPage({super.key, required this.mode});

  @override
  State<ReadingPage> createState() => _ReadingPageState();
}

class _ReadingPageState extends State<ReadingPage> {
  final model = Get.find<YijingModel>();
  Widget body = Container();
  String? reading;

  @override
  Widget build(BuildContext context) {
    final reader = ReaderBase.getReader(widget.mode);
    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    // 顯示解讀方式特點
    body = Center(
      child: MarkdownBody(
        styleSheet: markdownStyle(),
        data: reader.feature,
      ),
    );
    // 高島跟體用只適用一爻動的卦象
    if ((yijing.dongYao.length != 1) && (widget.mode == ReadingMode.gaodao || widget.mode == ReadingMode.tiyong)) {
      body = Center(
        child: Text(
          "這個卦有${yijing.dongYao.length}個動爻\n${widget.mode.name}只適合一爻動的卦象",
          textAlign: TextAlign.center,
        ),
      );
    } else {
      // 顯示解讀內容
      reading = model.getReading(widget.mode);
      if (reading != null) {
        body = MarkdownBody(
          styleSheet: markdownStyle(),
          data: reading!,
        );
      }
    }
    final setting = Get.find<SettingController>();
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          OverflowBar(
            alignment: MainAxisAlignment.end,
            spacing: 8,
            children: [
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
                onPressed: () async {
                  // 顯示加載指示器
                  Get.dialog(
                    const Center(
                      child: CircularProgressIndicator(),
                    ),
                    barrierDismissible: false,
                  );

                  final (sysPrompt, userPrompt) = reader.getPrompt(model);
                  var result = await setting.llmService.futureText(
                    apiKey: setting.apiKey!,
                    modelName: setting.modelName!,
                    messages: [
                      LlmMessage.system(sysPrompt),
                      LlmMessage.user(userPrompt),
                    ],
                  );

                  // 關閉加載指示器
                  Get.back();

                  result = reader.getMarkdownInner(result ?? '');
                  model.summary = reader.getSummary(result);
                  model.setReading(widget.mode, result);
                  await model.save();
                  setState(() {});
                },
                child: Text("重新解讀"),
              ),
              IconButton(
                onPressed: (reading == null)
                    ? null
                    : () {
                        FlutterClipboard.copy(reading!).then((value) {
                          Get.snackbar("複製成功", "複製成功");
                        });
                      },
                icon: Icon(Icons.copy_all),
              ),
            ],
          ),
          // 解讀顯示區
          body,
        ],
      ),
    );
  }
}
