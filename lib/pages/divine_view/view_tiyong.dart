import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_fortune/wu_calendar.dart';

import '../../core/assets.dart';
import '../../models/yijing_model.dart';

class ViewTiyong extends StatelessWidget {
  const ViewTiyong({super.key});

  @override
  Widget build(BuildContext context) {
    final model = Get.find<YijingModel>();
    final gzDict = GzDate.toDict(model.divineAtGzDate ?? '');
    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    if (yijing.dongYao.length != 1) {
      return Center(
        child: Text(
          "這個卦有${yijing.dongYao.length}個動爻\n體用只適合一爻動的卦象",
          textAlign: TextAlign.center,
        ),
      );
    }
    final layout = yijing.getMeiyiLayout(GanZhi.byName(gzDict["月"]));
    return Scaffold(
      body: SingleChildScrollView(
          padding: const EdgeInsets.all(8),
          child: Column(
            spacing: 8,
            children: [
              buildGua64Table(layout),
              buildGua8Table(layout),
            ],
          )),
    );
  }

  Widget buildGua64Table(Map layout) {
    final yongGua = layout["用卦"] as int;
    final gua64s = layout["八卦"] as List<Gua64>;
    return Table(
      columnWidths: {0: FixedColumnWidth(20)},
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      border: TableBorder.all(color: Colors.grey),
      children: [
        TableRow(children: [
          Container(),
          Text("本卦\n${gua64s[0].fullname}", textAlign: TextAlign.center),
          Text("互卦\n${gua64s[1].fullname}", textAlign: TextAlign.center),
          Text("變卦\n${gua64s[2].fullname}", textAlign: TextAlign.center),
        ]),
        TableRow(children: [
          Text(yongGua == 0 ? "用" : "體", textAlign: TextAlign.center),
          Assets.imgGua8(gua64s[0].gua8s[0].index, width: 100),
          Assets.imgGua8(gua64s[1].gua8s[0].index, width: 100),
          Assets.imgGua8(gua64s[2].gua8s[0].index, width: 100),
        ]),
        TableRow(children: [
          Text(yongGua == 1 ? "用" : "體", textAlign: TextAlign.center),
          Assets.imgGua8(gua64s[0].gua8s[1].index, width: 100),
          Assets.imgGua8(gua64s[1].gua8s[1].index, width: 100),
          Assets.imgGua8(gua64s[2].gua8s[1].index, width: 100),
        ]),
      ],
    );
  }

  Widget buildGua8Table(Map layout) {
    final gua8s = layout["單卦"] as List<Map<String, dynamic>>;
    return Table(
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      border: TableBorder.all(color: Colors.grey),
      children: [
        TableRow(
            children: List.generate(5, (index) {
          return Assets.imgGua8(gua8s[index]["卦序"], width: 40);
        })),
        TableRow(
            children: List.generate(5, (index) {
          return Text(gua8s[index]["五行"], textAlign: TextAlign.center);
        })),
        TableRow(
            children: List.generate(5, (index) {
          return Text(gua8s[index]["生剋"] ?? "", textAlign: TextAlign.center);
        })),
        TableRow(
            children: List.generate(5, (index) {
          return Text(gua8s[index]["吉凶"] ?? "", textAlign: TextAlign.center);
        })),
        TableRow(
            children: List.generate(5, (index) {
          final score = gua8s[index]["分數"];
          if (score == null) return Container();
          return Assets.imgLevel(score + 1, width: 40);
        })),
      ],
    );
  }
}
