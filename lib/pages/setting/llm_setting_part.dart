import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wu_llm/wu_llm.dart';

import '../../controllers/setting_controller.dart';

class LlmSettingPart extends StatelessWidget {
  LlmSettingPart({super.key});

  final apikeyCtrl = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 8,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildServiceRow(),
        buildApiUrlRow(),
        buildApiKeyRow(),
        buildModelRow(),
      ],
    );
  }

  Widget buildServiceRow() {
    return GetBuilder<SettingController>(builder: (setting) {
      return FormBuilderDropdown(
        name: "llmService",
        initialValue: setting.serviceName,
        items: setting.serviceList.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
        onChanged: (value) async {
          setting.serviceName = value ?? setting.serviceList.first;
          setting.update();
        },
      );
    });
  }

  Widget buildApiUrlRow() {
    return GetBuilder<SettingController>(builder: (setting) {
      final llm = setting.llmService;
      return Column(
        children: [
          TextButton(
              onPressed: () {
                launchUrl(Uri.parse(llm.apiKeyUrl));
              },
              child: Text("申請 API 金鑰：${llm.apiKeyUrl}")),
          Text("使用自己的金鑰可以不用受限於服務商的呼叫次數限制。", style: TextStyle(color: Colors.grey)),
        ],
      );
    });
  }

  Widget buildApiKeyRow() {
    return GetBuilder<SettingController>(builder: (setting) {
      apikeyCtrl.text = setting.apiKey ?? "";
      return FormBuilderTextField(
        name: "llmApiKey",
        controller: apikeyCtrl,
        decoration: InputDecoration(
          labelText: "API 金鑰",
          suffix: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 清除按鈕
              IconButton(
                onPressed: () => apikeyCtrl.clear(),
                icon: Icon(Icons.clear),
              ),
              // 刷新按鈕
              IconButton(
                  onPressed: () {
                    setting.apiKey = apikeyCtrl.text;
                  },
                  icon: Icon(Icons.refresh)),
            ],
          ),
        ),
        onChanged: (value) {
          setting.apiKey = value ?? "";
          setting.update();
        },
      );
    });
  }

  Widget buildModelRow() {
    return GetBuilder<SettingController>(builder: (setting) {
      final service = LlmBase.getService(setting.serviceName);
      final recommandColor = Colors.red;
      return FutureBuilder(
          future: service.getModels(setting.apiKey!),
          builder: (context, asyncSnapshot) {
            if (asyncSnapshot.connectionState != ConnectionState.done) {
              return const Center(child: CircularProgressIndicator());
            }
            final modelList = asyncSnapshot.data ?? [];
            return FormBuilderDropdown(
              name: "llmModel",
              initialValue: modelList.contains(setting.modelName) ? setting.modelName : null,
              items: modelList
                  .map(
                    (e) => DropdownMenuItem(
                      value: e,
                      child: Text(
                        e,
                        style: TextStyle(color: service.recommandModels.contains(e) ? recommandColor : null),
                      ),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setting.modelName = value ?? "";
                setting.update();
              },
            );
          });
    });
  }
}
