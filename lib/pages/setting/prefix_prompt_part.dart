import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';

import '../../controllers/setting_controller.dart';

class PrefixPromptPart extends StatelessWidget {
  const PrefixPromptPart({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingController>(builder: (setting) {
      return FormBuilderTextField(
        name: "prefixPrompt",
        decoration: InputDecoration(
          labelText: "前綴提示詞",
          hintText: "例如：請使用繁體中文解讀",
        ),
        minLines: 3,
        maxLines: 3,
        initialValue: setting.prefixPrompt ?? "請使用繁體中文解讀",
        onChanged: (value) {
          setting.prefixPrompt = value ?? "";
          setting.update();
        },
      );
    });
  }
}
