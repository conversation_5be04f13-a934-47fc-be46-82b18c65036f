// ignore_for_file: use_build_context_synchronously

import 'package:adv_yijing/core/zip_backup.dart';
import 'package:adv_yijing/hive/hivedb_yijing.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:path_provider/path_provider.dart';

import '../../controllers/setting_controller.dart';

class BackupPart extends StatelessWidget {
  final void Function()? onUpdated;
  const BackupPart({super.key, this.onUpdated});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        buildBackupPathField(),
        Row(
          mainAxisSize: MainAxisSize.max,
          spacing: 8,
          children: [
            Expanded(
              child: buildBackupButton(),
            ),
            Expanded(
              child: buildRestoreButton(),
            ),
          ],
        ),
      ],
    );
  }

  Widget buildBackupPathField() {
    return GetBuilder<SettingController>(builder: (setting) {
      final backupPathCtrl = TextEditingController(text: setting.backupPath);
      return FormBuilderTextField(
        name: "backupPath",
        decoration: InputDecoration(
          labelText: "備份資料夾",
          hintText: "請選擇備份資料夾",
          suffixIcon: IconButton(
            icon: Icon(Icons.folder_open),
            onPressed: () async {
              final extDir = await getExternalStorageDirectory();
              var path = await FilePicker.platform.getDirectoryPath(initialDirectory: extDir?.path);

              if (path == null) return;
              backupPathCtrl.text = path;
            },
          ),
        ),
        controller: backupPathCtrl,
        onChanged: (value) {
          setting.backupPath = value ?? "";
          setting.update();
        },
      );
    });
  }

  Widget buildBackupButton() {
    final colorScheme = Get.theme.colorScheme;
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
    );
    final setting = Get.find<SettingController>();
    return ElevatedButton(
      style: buttonStyle,
      onPressed: () async {
        try {
          final zhibackup = ZipBackup(setting.backupPath!, "adv_yijing_backup.zip");
          await zhibackup.backup((tempDirPath) async {
            await setting.jsonBackup();
            final hivePath = await HiveDBYijing().backupHiveBox(setting.backupPath!);
            return [
              setting.backupFileName,
              hivePath,
            ];
          });
          Get.snackbar(
            "設定備份",
            "設定備份已完成！",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.secondary,
            colorText: colorScheme.onSecondary,
          );
        } catch (e) {
          print(e);
          Get.snackbar(
            "備份失敗",
            e.toString(),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.error,
            colorText: colorScheme.onError,
          );
        }
      },
      child: const Text("備份"),
    );
  }

  Widget buildRestoreButton() {
    final colorScheme = Get.theme.colorScheme;
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
    );
    final setting = Get.find<SettingController>();
    return ElevatedButton(
      style: buttonStyle,
      onPressed: () async {
        try {
          final zhibackup = ZipBackup(setting.backupPath!, "adv_yijing_backup.zip");
          await zhibackup.restore((restoredFilePath) async {
            if (restoredFilePath.endsWith(setting.backupFileName)) {
              await setting.jsonRestore();
            } else if (restoredFilePath.endsWith('${HiveDBYijing().boxName}.hive')) {
              // 將 hive 檔案從暫存目錄複製到正確位置
              final tempDir = await getTemporaryDirectory();
              await HiveDBYijing().restoreHiveBox(tempDir.path);
            }
          });
          Get.snackbar(
            "設定還原",
            "設定已使用備份還原！",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.secondary,
            colorText: colorScheme.onSecondary,
          );
          onUpdated?.call();
        } catch (e) {
          Get.snackbar(
            "還原失敗",
            e.toString(),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.error,
            colorText: colorScheme.onError,
          );
        }
      },
      child: const Text("還原"),
    );
  }
}
