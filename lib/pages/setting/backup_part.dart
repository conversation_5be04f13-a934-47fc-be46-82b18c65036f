// ignore_for_file: use_build_context_synchronously

import 'package:adv_yijing/hive/hivedb_yijing.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wu_core/wu_backup.dart';

import '../../controllers/setting_controller.dart';

class BackupPart extends StatelessWidget {
  final void Function()? onUpdated;
  const BackupPart({super.key, this.onUpdated});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        buildBackupPathField(),
        Row(
          mainAxisSize: MainAxisSize.max,
          spacing: 8,
          children: [
            Expanded(
              child: buildBackupButton(),
            ),
            Expanded(
              child: buildRestoreButton(),
            ),
          ],
        ),
        const SizedBox(height: 8),
        buildManualRestoreButton(),
      ],
    );
  }

  Widget buildBackupPathField() {
    return GetBuilder<SettingController>(builder: (setting) {
      final backupPathCtrl = TextEditingController(text: setting.backupPath);
      return FormBuilderTextField(
        name: "backupPath",
        decoration: InputDecoration(
          labelText: "備份資料夾",
          hintText: "自動選擇安全的備份位置",
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(Icons.auto_fix_high),
                tooltip: "使用安全備份目錄",
                onPressed: () async {
                  final safeDir = await PermissionHelper.getSafeBackupDirectory();
                  backupPathCtrl.text = safeDir;
                  setting.backupPath = safeDir;
                  setting.update();
                },
              ),
              IconButton(
                icon: Icon(Icons.folder_open),
                tooltip: "手動選擇目錄",
                onPressed: () async {
                  // 先嘗試請求權限
                  await PermissionHelper.requestStoragePermissionSilently();

                  final safeDir = await PermissionHelper.getSafeBackupDirectory();
                  var path = await FilePicker.platform.getDirectoryPath(
                    initialDirectory: safeDir,
                  );

                  if (path == null) return;
                  backupPathCtrl.text = path;
                  setting.backupPath = path;
                  setting.update();
                },
              ),
            ],
          ),
        ),
        controller: backupPathCtrl,
        onChanged: (value) {
          setting.backupPath = value ?? "";
          setting.update();
        },
      );
    });
  }

  Widget buildBackupButton() {
    final colorScheme = Get.theme.colorScheme;
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
    );
    final setting = Get.find<SettingController>();
    return ElevatedButton(
      style: buttonStyle,
      onPressed: () async {
        try {
          // 如果沒有設定備份路徑，使用安全的備份目錄
          String backupPath = setting.backupPath ?? "";
          if (backupPath.isEmpty) {
            backupPath = await PermissionHelper.getSafeBackupDirectory();
            setting.backupPath = backupPath;
            setting.update();
          }

          final zhibackup = ZipBackup(backupPath, "adv_yijing_backup.zip");
          await zhibackup.backup((tempDirPath) async {
            await setting.jsonBackup();
            final hivePath = await HiveDBYijing().backupHiveBox(backupPath);
            return [
              setting.backupFileName,
              hivePath,
            ];
          });
          Get.snackbar(
            "設定備份",
            "設定備份已完成！",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.secondary,
            colorText: colorScheme.onSecondary,
          );
        } catch (e) {
          Get.snackbar(
            "備份失敗",
            e.toString(),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.error,
            colorText: colorScheme.onError,
          );
        }
      },
      child: const Text("備份"),
    );
  }

  Widget buildRestoreButton() {
    final colorScheme = Get.theme.colorScheme;
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
    );
    final setting = Get.find<SettingController>();
    return ElevatedButton(
      style: buttonStyle,
      onPressed: () async {
        try {
          // 如果沒有設定備份路徑，使用安全的備份目錄
          String backupPath = setting.backupPath ?? "";
          if (backupPath.isEmpty) {
            backupPath = await PermissionHelper.getSafeBackupDirectory();
            setting.backupPath = backupPath;
            setting.update();
          }

          final zhibackup = ZipBackup(backupPath, "adv_yijing_backup.zip");
          await zhibackup.restore((restoredFilePath) async {
            if (restoredFilePath.endsWith(setting.backupFileName)) {
              await setting.jsonRestore();
            } else if (restoredFilePath.endsWith('${HiveDBYijing().boxName}.hive')) {
              // 將 hive 檔案從暫存目錄複製到正確位置
              final tempDir = await getTemporaryDirectory();
              await HiveDBYijing().restoreHiveBox(tempDir.path);
            }
          });
          Get.snackbar(
            "設定還原",
            "設定已使用備份還原！",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.secondary,
            colorText: colorScheme.onSecondary,
          );
          onUpdated?.call();
        } catch (e) {
          Get.snackbar(
            "還原失敗",
            e.toString(),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: colorScheme.error,
            colorText: colorScheme.onError,
          );
        }
      },
      child: const Text("還原"),
    );
  }

  Widget buildManualRestoreButton() {
    final colorScheme = Get.theme.colorScheme;
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: colorScheme.tertiary,
      foregroundColor: colorScheme.onTertiary,
    );
    final setting = Get.find<SettingController>();
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        style: buttonStyle,
        icon: const Icon(Icons.file_open),
        label: const Text("手動選擇備份檔案還原"),
        onPressed: () async {
          try {
            // 如果沒有設定備份路徑，使用安全的備份目錄
            String backupPath = setting.backupPath ?? "";
            if (backupPath.isEmpty) {
              backupPath = await PermissionHelper.getSafeBackupDirectory();
              setting.backupPath = backupPath;
              setting.update();
            }

            final zhibackup = ZipBackup(backupPath, "adv_yijing_backup.zip");
            await zhibackup.restoreWithPicker((restoredFilePath) async {
              if (restoredFilePath.endsWith(setting.backupFileName)) {
                await setting.jsonRestore();
              } else if (restoredFilePath.endsWith('${HiveDBYijing().boxName}.hive')) {
                // 將 hive 檔案從暫存目錄複製到正確位置
                final tempDir = await getTemporaryDirectory();
                await HiveDBYijing().restoreHiveBox(tempDir.path);
              }
            });
            Get.snackbar(
              "設定還原",
              "設定已使用備份還原！",
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: colorScheme.secondary,
              colorText: colorScheme.onSecondary,
            );
            onUpdated?.call();
          } catch (e) {
            Get.snackbar(
              "還原失敗",
              e.toString(),
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: colorScheme.error,
              colorText: colorScheme.onError,
            );
          }
        },
      ),
    );
  }
}
