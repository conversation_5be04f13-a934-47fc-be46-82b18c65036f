import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'backup_part.dart';
import 'llm_setting_part.dart';
import 'prefix_prompt_part.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Get.theme.colorScheme;
    return Scaffold(
      appBar: AppBar(
        title: const Text('設定'),
      ),
      body: ListView(
        children: [
          Card(
            color: colorScheme.secondaryContainer,
            clipBehavior: Clip.antiAlias,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: LlmSettingPart(),
            ),
          ),
          Card(
            color: colorScheme.secondaryContainer,
            clipBehavior: Clip.antiAlias,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: PrefixPromptPart(),
            ),
          ),
          Card(
            color: colorScheme.secondaryContainer,
            clipBehavior: Clip.antiAlias,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: BackupPart(onUpdated: () => setState(() {})),
            ),
          ),
        ],
      ),
    );
  }
}
